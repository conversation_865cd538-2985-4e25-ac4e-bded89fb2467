import express from 'express'
import request from 'supertest'
import { afterEach, beforeEach, describe, expect, it } from 'vitest'
import * as userController from '../../controllers/userController'
import { cleanupTestEmployees, createTestEmployee } from '../setup'

// Create test app
const app = express()
app.use(express.json())

// Add routes
app.post('/api/v1/users/setup-oauth', userController.setupOAuthAccess)
app.put('/api/v1/users/:id', userController.updateUser)
app.get('/api/v1/users/:id', userController.getUserById)
app.delete(
  '/api/v1/users/:id',
  userController.removeUsernameAndPasswordFromUser
)
app.post('/api/v1/users/:id/change-password', userController.changePassword)

describe('UserController', () => {
  let testEmployeeId: number

  beforeEach(async () => {
    testEmployeeId = await createTestEmployee({
      first_name: 'API',
      last_name: 'User',
      email: '<EMAIL>',
    })
  })

  afterEach(async () => {
    await cleanupTestEmployees()
  })

  describe('POST /api/v1/users/setup-oauth', () => {
    it('should setup OAuth access with valid data', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: 'apiuser123',
          password: 'testPassword123',
        })

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty(
        'message',
        'OAuth access configured successfully'
      )
      expect(response.body.employee).toHaveProperty(
        'employee_id',
        testEmployeeId
      )
      expect(response.body.employee).toHaveProperty('username', 'apiuser123')
    })

    it('should reject OAuth setup with invalid username containing @', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: '<EMAIL>',
          password: 'testPassword123',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Username cannot contain @ character'
      )
    })

    it('should reject OAuth setup with username containing only numbers', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: '12345',
          password: 'testPassword123',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Username must contain at least 1 alphabetic character'
      )
    })

    it('should reject OAuth setup with empty username', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: '',
          password: 'testPassword123',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain('Username cannot be empty')
    })

    it('should setup OAuth access without username', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          password: 'testPassword123',
        })

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty(
        'message',
        'OAuth access configured successfully'
      )
      expect(response.body.employee).toHaveProperty(
        'employee_id',
        testEmployeeId
      )
    })

    it('should reject OAuth setup with missing employee_id', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          username: 'validuser',
          password: 'testPassword123',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Employee ID and password are required'
      )
    })

    it('should reject OAuth setup with missing password', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: 'validuser',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Employee ID and password are required'
      )
    })

    it('should reject OAuth setup for non-existent employee', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: 99999,
          username: 'validuser',
          password: 'testPassword123',
        })

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
      expect(response.body.message).toContain(
        'Employee must be created by HR system first'
      )
    })
  })

  describe('PUT /api/v1/users/:id', () => {
    beforeEach(async () => {
      // Set up initial OAuth access
      await request(app).post('/api/v1/users/setup-oauth').send({
        employee_id: testEmployeeId,
        username: 'initialuser',
        password: 'initialPassword123',
      })
    })

    it('should update username with valid data', async () => {
      const response = await request(app)
        .put(`/api/v1/users/${testEmployeeId}`)
        .send({
          username: 'updateduser123',
        })

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('username', 'updateduser123')
    })

    it('should reject username update with invalid username', async () => {
      const response = await request(app)
        .put(`/api/users/${testEmployeeId}`)
        .send({
          username: '<EMAIL>',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Username cannot contain @ character'
      )
    })

    it('should update password', async () => {
      const response = await request(app)
        .put(`/api/users/${testEmployeeId}`)
        .send({
          password: 'newPassword456',
        })

      expect(response.status).toBe(200)
    })

    it('should update both username and password', async () => {
      const response = await request(app)
        .put(`/api/users/${testEmployeeId}`)
        .send({
          username: 'newuser456',
          password: 'newPassword456',
        })

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('username', 'newuser456')
    })

    it('should reject update for non-existent employee', async () => {
      const response = await request(app).put('/api/users/99999').send({
        username: 'validuser',
      })

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
      expect(response.body.message).toContain('Employee not found')
    })

    it('should reject invalid employee ID', async () => {
      const response = await request(app).put('/api/users/invalid').send({
        username: 'validuser',
      })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain('Invalid employee ID')
    })
  })

  describe('GET /api/users/:id', () => {
    beforeEach(async () => {
      // Set up initial user
      await request(app).post('/api/users').send({
        employee_id: testEmployeeId,
        username: 'getuser',
        password: 'getPassword123',
      })
    })

    it('should get user by ID', async () => {
      const response = await request(app).get(`/api/users/${testEmployeeId}`)

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('employee_id', testEmployeeId)
      expect(response.body).toHaveProperty('username', 'getuser')
      expect(response.body).toHaveProperty('first_name', 'API')
      expect(response.body).toHaveProperty('last_name', 'User')
    })

    it('should return 404 for non-existent user', async () => {
      const response = await request(app).get('/api/users/99999')

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
    })

    it('should reject invalid employee ID', async () => {
      const response = await request(app).get('/api/users/invalid')

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
    })
  })
})
