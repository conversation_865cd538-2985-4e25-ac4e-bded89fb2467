import { Router } from 'express'
import * as userController from '../../controllers/userController'

export const userRouter = Router()

// GET /api/v1/users/me - Get current authenticated user
userRouter.get('/me', userController.getCurrentUser)

// POST /api/v1/users/setup-oauth - Set up OAuth access for existing employee
userRouter.post('/setup-oauth', userController.setupOAuthAccess)

// PUT /api/v1/users/:id - Update user OAuth settings (username, password)
userRouter.put('/:id', userController.updateUser)

// GET /api/v1/users/:id - Get user by ID
userRouter.get('/:id', userController.getUserById)

// DELETE /api/v1/users/:id - Remove OAuth access for user
userRouter.delete('/:id', userController.removeUsernameAndPasswordFromUser)

// POST /api/v1/users/:id/change-password - Change user password
userRouter.post('/:id/change-password', userController.changePassword)
