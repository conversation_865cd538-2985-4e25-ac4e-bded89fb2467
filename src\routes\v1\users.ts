import { Router } from 'express'
import * as employeeController from '../../controllers/employeeController'

export const employeeRouter = Router()

// GET /api/v1/employees/me - Get current authenticated employee
employeeRouter.get('/me', employeeController.getCurrentEmployee)

// POST /api/v1/employees/setup-oauth - Set up OAuth access for existing employee
employeeRouter.post('/setup-oauth', employeeController.setupOAuthAccess)

// PUT /api/v1/employees/:id - Update employee OAuth settings (username, password)
employeeRouter.put('/:id', employeeController.updateEmployee)

// GET /api/v1/employees/:id - Get employee by ID
employeeRouter.get('/:id', employeeController.getEmployeeById)

// DELETE /api/v1/employees/:id - Remove OAuth access for employee
employeeRouter.delete('/:id', employeeController.removeOAuthAccess)

// POST /api/v1/employees/:id/change-password - Change employee password
employeeRouter.post('/:id/change-password', employeeController.changePassword)
