import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import request from 'supertest'
import express from 'express'
import { setupOAuthAccess } from '../../controllers/userController'
import { createTestEmployee, cleanupTestEmployees } from '../setup'

// Create test app
const app = express()
app.use(express.json())

// Add route
app.post('/api/v1/users/setup-oauth', setupOAuthAccess)

describe('OAuth Setup Integration', () => {
  let testEmployeeId: number

  beforeEach(async () => {
    testEmployeeId = await createTestEmployee({
      first_name: 'OAuth',
      last_name: 'Test',
      email: '<EMAIL>'
    })
  })

  afterEach(async () => {
    await cleanupTestEmployees()
  })

  describe('OAuth Access Setup', () => {
    it('should successfully set up OAuth access for existing employee', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: 'oauthuser123',
          password: 'testPassword123'
        })

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty('message', 'OAuth access configured successfully')
      expect(response.body.employee).toHaveProperty('employee_id', testEmployeeId)
      expect(response.body.employee).toHaveProperty('username', 'oauthuser123')
      expect(response.body.employee).toHaveProperty('first_name', 'OAuth')
      expect(response.body.employee).toHaveProperty('last_name', 'Test')
    })

    it('should set up OAuth access without username', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          password: 'testPassword123'
        })

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty('message', 'OAuth access configured successfully')
      expect(response.body.employee).toHaveProperty('employee_id', testEmployeeId)
      expect(response.body.employee).toHaveProperty('first_name', 'OAuth')
      expect(response.body.employee).toHaveProperty('last_name', 'Test')
      // Username should be null since we didn't provide one
      expect(response.body.employee.username).toBeNull()
    })

    it('should reject setup for non-existent employee', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: 99999,
          username: 'testuser',
          password: 'testPassword123'
        })

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
      expect(response.body.message).toContain('Employee must be created by HR system first')
    })

    it('should validate username rules during setup', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          username: '<EMAIL>',
          password: 'testPassword123'
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain('Username cannot contain @ character')
    })

    it('should require both employee_id and password', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          username: 'testuser'
          // Missing employee_id and password
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain('Employee ID and password are required')
    })
  })

  describe('Business Logic Validation', () => {
    it('should clarify this is for OAuth setup, not employee creation', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: 99999,
          password: 'testPassword123'
        })

      expect(response.status).toBe(404)
      expect(response.body.message).toContain('HR system')
      expect(response.body.message).not.toContain('create')
      expect(response.body.message).toContain('Employee must be created by HR system first')
    })

    it('should return proper success message indicating OAuth configuration', async () => {
      const response = await request(app)
        .post('/api/v1/users/setup-oauth')
        .send({
          employee_id: testEmployeeId,
          password: 'testPassword123'
        })

      expect(response.status).toBe(201)
      expect(response.body.message).toBe('OAuth access configured successfully')
      expect(response.body.message).not.toContain('created')
      expect(response.body.message).not.toContain('user created')
    })
  })
})
