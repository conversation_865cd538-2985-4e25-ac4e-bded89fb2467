import { Kysely, sql } from 'kysely'

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema
    .createTable('password_reset_codes')
    .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
    .addColumn('employee_id', 'integer', (col) => col.notNull())
    .addColumn('reset_code', 'varchar(6)', (col) => col.notNull())
    .addColumn('expires_at', 'datetime', (col) => col.notNull())
    .addColumn('used', 'boolean', (col) => col.notNull().defaultTo(false))
    .addColumn('created_at', 'datetime', (col) => 
      col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`)
    )
    .addColumn('used_at', 'datetime')
    .execute()

  // Add foreign key constraint
  await db.schema
    .createIndex('idx_password_reset_codes_employee_id')
    .on('password_reset_codes')
    .column('employee_id')
    .execute()

  // Add index on reset_code for fast lookups
  await db.schema
    .createIndex('idx_password_reset_codes_reset_code')
    .on('password_reset_codes')
    .column('reset_code')
    .execute()

  // Add index on expires_at for cleanup
  await db.schema
    .createIndex('idx_password_reset_codes_expires_at')
    .on('password_reset_codes')
    .column('expires_at')
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('password_reset_codes').execute()
}
