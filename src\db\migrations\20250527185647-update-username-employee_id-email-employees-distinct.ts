import type { <PERSON>ys<PERSON> } from 'kysely'

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // modify username to be unique
  await db.schema
    .alterTable('employees')
    .modifyColumn('username', 'varchar(255)', (col) => col.unique())
    .execute()

  // set unique constraint on email
  await db.schema
    .alterTable('employees')
    .modifyColumn('email', 'varchar(255)', (col) => col.unique())
    .execute()

  // set employee_id to be unique and not null
  await db.schema
    .alterTable('employees')
    .modifyColumn('employee_id', 'integer', (col) => col.unique().notNull())
    .execute()
}

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function down(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // remove unique constraint on username
  await db.schema
    .alterTable('employees')
    .modifyColumn('username', 'varchar(255)')
    .execute()

  // remove unique constraint on email
  await db.schema
    .alterTable('employees')
    .modifyColumn('email', 'varchar(255)')
    .execute()

  // remove unique constraint on employee_id
  await db.schema
    .alterTable('employees')
    .modifyColumn('employee_id', 'integer', (col) => col.notNull())
    .execute()
}
