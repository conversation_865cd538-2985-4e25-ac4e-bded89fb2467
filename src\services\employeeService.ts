import * as bcrypt from 'bcrypt'
import { db } from '../db/database'
import { Employee, EmployeeUpdate } from '../db/models/employees'

interface EmployeeResponse {
  employee_id: number
  status: number
  first_name: string
  last_name: string
  department: string
  short_name: string | null
  nickname: string | null
  email: string | null
  work_area_id: number | null
  employment_date: string | Date
  image: string | null
  employee_contact: string | null
  emergency_number: string | null
  gender: string | null
  barcode: number
  created_at: string | Date
  updated_at: string | Date
  emp_barcode: number | null
  rfid_code: number | null
  section: string | null
  title: string | null
  departments_id: number | null
  title_id: number | null
  username: string | null
  last_login_at: string | Date | null
  failed_login_attempts: number
  locked_until: string | Date | null
}

export class EmployeeService {
  private readonly saltRounds = 12

  /**
   * Validates username according to business rules:
   * - Must have at least 1 alphabetic character
   * - Cannot contain @ character
   */
  private validateUsername(username: string): {
    isValid: boolean
    error?: string
  } {
    if (!username || username.trim().length === 0) {
      return { isValid: false, error: 'Username cannot be empty' }
    }

    // Check if username contains @ character
    if (username.includes('@')) {
      return { isValid: false, error: 'Username cannot contain @ character' }
    }

    // Check if username has at least 1 alphabetic character
    const hasAlphaChar = /[a-zA-Z]/.test(username)
    if (!hasAlphaChar) {
      return {
        isValid: false,
        error: 'Username must contain at least 1 alphabetic character',
      }
    }

    return { isValid: true }
  }

  async getAllEmployees(): Promise<EmployeeResponse[]> {
    const employees = await db
      .selectFrom('employees')
      .select([
        'employee_id',
        'status',
        'first_name',
        'last_name',
        'department',
        'short_name',
        'nickname',
        'email',
        'work_area_id',
        'employment_date',
        'image',
        'employee_contact',
        'emergency_number',
        'gender',
        'barcode',
        'created_at',
        'updated_at',
        'emp_barcode',
        'rfid_code',
        'section',
        'title',
        'departments_id',
        'title_id',
        'username',
        'last_login_at',
        'failed_login_attempts',
        'locked_until',
      ])
      .where('status', '=', 1) // Only active employees
      .orderBy('created_at', 'desc')
      .execute()

    return employees
  }

  async getEmployeeById(
    employee_id: number
  ): Promise<EmployeeResponse | undefined> {
    const employee = await db
      .selectFrom('employees')
      .select([
        'employee_id',
        'status',
        'first_name',
        'last_name',
        'department',
        'short_name',
        'nickname',
        'email',
        'work_area_id',
        'employment_date',
        'image',
        'employee_contact',
        'emergency_number',
        'gender',
        'barcode',
        'created_at',
        'updated_at',
        'emp_barcode',
        'rfid_code',
        'section',
        'title',
        'departments_id',
        'title_id',
        'username',
        'last_login_at',
        'failed_login_attempts',
        'locked_until',
      ])
      .where('employee_id', '=', employee_id)
      .where('status', '=', 1)
      .executeTakeFirst()

    return employee
  }

  async getEmployeeByUsername(username: string): Promise<Employee | undefined> {
    return await db
      .selectFrom('employees')
      .selectAll()
      .where('username', '=', username)
      .where('status', '=', 1)
      .executeTakeFirst()
  }

  async getEmployeeByEmail(email: string): Promise<Employee | undefined> {
    return await db
      .selectFrom('employees')
      .selectAll()
      .where('email', '=', email)
      .where('status', '=', 1)
      .executeTakeFirst()
  }

  async updateEmployeePassword(
    employee_id: number,
    password: string
  ): Promise<boolean> {
    const passwordHash = await bcrypt.hash(password, this.saltRounds)

    const result = await db
      .updateTable('employees')
      .set({
        password_hash: passwordHash,
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    return result.numUpdatedRows > 0
  }

  async updateEmployee(
    employee_id: number,
    updates: EmployeeUpdate
  ): Promise<EmployeeResponse | undefined> {
    const updateData: EmployeeUpdate = {
      ...updates,
      updated_at: new Date().toISOString(),
    }

    // Validate username if provided
    if (updates.username) {
      const validation = this.validateUsername(updates.username)
      if (!validation.isValid) {
        throw new Error(validation.error)
      }

      // Check if username already exists
      const existingEmployee = await this.getEmployeeByUsername(
        updates.username
      )
      if (existingEmployee && existingEmployee.employee_id !== employee_id) {
        throw new Error('Username already exists')
      }
    }

    // Hash password if provided
    if ('password' in updates && updates.password) {
      updateData.password_hash = await bcrypt.hash(
        updates.password as string,
        this.saltRounds
      )
      delete (updateData as EmployeeUpdate & { password?: string }).password // Remove plain password from update data
    }

    const result = await db
      .updateTable('employees')
      .set(updateData)
      .where('employee_id', '=', employee_id)
      .returning([
        'employee_id',
        'status',
        'first_name',
        'last_name',
        'department',
        'short_name',
        'nickname',
        'email',
        'work_area_id',
        'employment_date',
        'image',
        'employee_contact',
        'emergency_number',
        'gender',
        'barcode',
        'created_at',
        'updated_at',
        'emp_barcode',
        'rfid_code',
        'section',
        'title',
        'departments_id',
        'title_id',
        'username',
        'last_login_at',
        'failed_login_attempts',
        'locked_until',
      ])
      .executeTakeFirst()

    return result
  }

  async validateEmployee(
    username: string,
    password: string
  ): Promise<EmployeeResponse | null> {
    // Try to find by username first, then by email
    let employee = await this.getEmployeeByUsername(username)
    if (!employee) {
      employee = await this.getEmployeeByEmail(username)
    }

    if (!employee || !employee.password_hash) {
      return null
    }

    // Check if account is locked
    if (employee.locked_until && new Date(employee.locked_until) > new Date()) {
      return null
    }

    const isValidPassword = await bcrypt.compare(
      password,
      employee.password_hash
    )

    if (!isValidPassword) {
      // Increment failed login attempts
      await this.incrementFailedLoginAttempts(employee.employee_id)
      return null
    }

    // Reset failed login attempts and update last login
    await this.resetFailedLoginAttempts(employee.employee_id)

    // Return employee without sensitive data
    const { password_hash, dui, nit, isss, ...employeeResponse } = employee
    return employeeResponse
  }

  async incrementFailedLoginAttempts(employee_id: number): Promise<void> {
    const employee = await db
      .selectFrom('employees')
      .select(['failed_login_attempts'])
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    if (!employee) return

    const newAttempts = (employee.failed_login_attempts || 0) + 1
    const lockUntil =
      newAttempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : null // Lock for 30 minutes after 5 attempts

    await db
      .updateTable('employees')
      .set({
        failed_login_attempts: newAttempts,
        locked_until: lockUntil?.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .execute()
  }

  async resetFailedLoginAttempts(employee_id: number): Promise<void> {
    await db
      .updateTable('employees')
      .set({
        failed_login_attempts: 0,
        locked_until: null,
        last_login_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .execute()
  }

  async changePassword(
    employee_id: number,
    currentPassword: string,
    newPassword: string
  ): Promise<boolean> {
    const employee = await db
      .selectFrom('employees')
      .selectAll()
      .where('employee_id', '=', employee_id)
      .where('status', '=', 1)
      .executeTakeFirst()

    if (!employee || !employee.password_hash) {
      return false
    }

    const isValidCurrentPassword = await bcrypt.compare(
      currentPassword,
      employee.password_hash
    )

    if (!isValidCurrentPassword) {
      return false
    }

    return await this.updateEmployeePassword(employee_id, newPassword)
  }

  async setUsername(
    employee_id: number,
    username: string
  ): Promise<{ success: boolean; error?: string }> {
    // Validate username format
    const validation = this.validateUsername(username)
    if (!validation.isValid) {
      return { success: false, error: validation.error }
    }

    // Check if username already exists
    const existingEmployee = await this.getEmployeeByUsername(username)
    if (existingEmployee && existingEmployee.employee_id !== employee_id) {
      return { success: false, error: 'Username already exists' }
    }

    const result = await db
      .updateTable('employees')
      .set({
        username,
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    return { success: result.numUpdatedRows > 0 }
  }
}

export const employeeService = new EmployeeService()
