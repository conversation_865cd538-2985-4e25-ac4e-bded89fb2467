import { db } from '../db/database'
import { employeeService } from '../services/employeeService'
import { oauthClientService } from '../services/oauthClientService'

export async function seedDatabase() {
  try {
    console.log('Starting database seeding...')

    // Find existing employees to set up OAuth credentials
    const existingEmployees = await db
      .selectFrom('employees')
      .select(['employee_id', 'email', 'first_name', 'last_name', 'username'])
      .where('status', '=', 1)
      .limit(5)
      .execute()

    if (existingEmployees.length === 0) {
      console.log(
        'No existing employees found. Please add employees to the database first.'
      )
      return
    }

    // Set up OAuth credentials for the first few employees
    for (let i = 0; i < Math.min(2, existingEmployees.length); i++) {
      const employee = existingEmployees[i]

      // Set username if not already set
      if (!employee.username) {
        const username = `${employee.first_name.toLowerCase()}.${employee.last_name.toLowerCase()}`
        await employeeService.setUsername(employee.employee_id, username)
        console.log(
          `Set username for employee ${employee.employee_id}: ${username}`
        )
      }

      // Set password for OAuth authentication
      const password = i === 0 ? 'admin123' : 'password123'
      await employeeService.updateEmployeePassword(
        employee.employee_id,
        password
      )
      console.log(
        `Set password for employee ${employee.employee_id} (${employee.username || employee.email}): ${password}`
      )
    }

    // Create a test OAuth client for authorization code flow
    const existingWebClient = await db
      .selectFrom('oauth_clients')
      .selectAll()
      .where('client_name', '=', 'Test Web Application')
      .executeTakeFirst()

    if (!existingWebClient) {
      const webClient = await oauthClientService.createClient({
        client_name: 'Test Web Application',
        redirect_uris: [
          'http://localhost:3000/callback',
          'http://localhost:5173/callback',
        ],
        grant_types: ['authorization_code', 'refresh_token'],
        scope: 'read write',
      })
      console.log('Created web client:', webClient.client_name)
      console.log('Client ID:', webClient.client_id)
      console.log('Client Secret:', webClient.client_secret)
    } else {
      console.log('Web client already exists')
    }

    // Create a test OAuth client for password flow
    const existingMobileClient = await db
      .selectFrom('oauth_clients')
      .selectAll()
      .where('client_name', '=', 'Test Mobile Application')
      .executeTakeFirst()

    if (!existingMobileClient) {
      const mobileClient = await oauthClientService.createClient({
        client_name: 'Test Mobile Application',
        redirect_uris: ['app://callback'],
        grant_types: ['password', 'refresh_token'],
        scope: 'read write',
      })
      console.log('Created mobile client:', mobileClient.client_name)
      console.log('Client ID:', mobileClient.client_id)
      console.log('Client Secret:', mobileClient.client_secret)
    } else {
      console.log('Mobile client already exists')
    }

    console.log('\nDatabase seeding completed successfully!')
    console.log('\nTest credentials:')
    if (existingEmployees.length > 0) {
      const firstEmployee = existingEmployees[0]
      console.log(`Username: ${firstEmployee.username || firstEmployee.email}`)
      console.log('Password: admin123')
    }
    if (existingEmployees.length > 1) {
      const secondEmployee = existingEmployees[1]
      console.log(
        `Username: ${secondEmployee.username || secondEmployee.email}`
      )
      console.log('Password: password123')
    }
  } catch (error) {
    console.error('Error seeding database:', error)
    throw error
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding finished')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
