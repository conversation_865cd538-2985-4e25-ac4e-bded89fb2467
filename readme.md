# OAuth 2.0 Authentication Server with RS256 & Client Example

This project provides a **complete OAuth 2.0 ecosystem** with RS256 JWT tokens, including:

- **OAuth 2.0 Authorization Server** (Node.js/Express)
- **Example Client Application** (React/Vite)
- **Comprehensive Documentation** and examples

## 🎯 **Project Overview**

```
varpro_auth/
├── server/                 # OAuth 2.0 Authorization Server
│   ├── src/
│   │   ├── controllers/    # API endpoints
│   │   ├── services/       # Business logic
│   │   ├── db/            # Database models & migrations
│   │   ├── routes/        # Express routes
│   │   └── utils/         # Utilities & key generation
│   ├── keys/              # RSA key pairs for RS256
│   └── package.json
├── client/                # Original client (if exists)
├── client-app-example/    # Comprehensive React example
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── contexts/      # Authentication context
│   │   ├── pages/         # Route pages
│   │   ├── services/      # OAuth integration
│   │   └── config/        # Configuration

- Authorization Code Flow (with PKCE support)
- Resource Owner Password Credentials Flow
- Refresh Token Flow

## Setup

This creates:

- Test users (testuser/password123, admin/admin123)
- Sample OAuth clients for web and mobile applications

## OAuth 2.0 Endpoints

### Authorization Endpoint

```

GET /oauth/authorize?response_type=code&client_id={client_id}&redirect_uri={redirect_uri}&scope={scope}&state={state}

```

### Token Endpoint

```

POST /oauth/token
Content-Type: application/x-www-form-urlencoded

# Authorization Code Flow

grant_type=authorization_code&code={code}&redirect_uri={redirect_uri}&client_id={client_id}&client_secret={client_secret}

# Password Flow

grant_type=password&username={username}&password={password}&client_id={client_id}&client_secret={client_secret}&scope={scope}

# Refresh Token

grant_type=refresh_token&refresh_token={refresh_token}&client_id={client_id}&client_secret={client_secret}

```

### Token Revocation

```

POST /oauth/revoke
Content-Type: application/x-www-form-urlencoded

token={access_token_or_refresh_token}&client_id={client_id}&client_secret={client_secret}

````

## DB

### Migration

To create a migration
`npx kysely migrate:make table-some_table_name`
`npx kysely migrate:make column-some_column-some_table_name`

To run a single migration
`npx kysely migrate:up`

To run all unrun migrations
`npx kysely migrate:latest`

To undo the last migration run
`npx kysely migrate:down`

### Database Utilities

To inspect database tables
`npm run db:inspect tables`
`npm run db:inspect employees`

To seed the database with test data
`npm run db:seed`

## Employee Management

### Username Validation Rules

Usernames must follow these rules:
- **Must contain at least 1 alphabetic character** (a-z, A-Z)
- **Cannot contain the @ character**
- Cannot be empty or whitespace only

Valid examples: `john.doe`, `user123`, `employee_1`, `admin-user`
Invalid examples: `<EMAIL>`, `12345`, `@username`, `username@`

### Update Employee Password

To update an employee's password using their employee_id:

```bash
npm run employee:update-password <employee_id> <new_password>
```

Examples:
```bash
npm run employee:update-password 123 "newSecurePassword123"
npm run employee:update-password 456 "anotherPassword456"
```

Note: Wrap passwords in quotes if they contain special characters.

### Testing

The project uses Vitest for testing with comprehensive test suites covering:

#### Run Tests

```bash
npm test                    # Run tests in watch mode
npm run test:run           # Run tests once
npm run test:ui            # Run tests with UI interface
npm run test:coverage      # Run tests with coverage report
npm run test:watch         # Run tests in watch mode (explicit)
```

#### Test Suites

- **Username Validation Tests**: Comprehensive validation rule testing
- **Employee Service Tests**: Database operations and business logic
- **OAuth Token Service Tests**: JWT token creation and validation
- **OAuth Client Service Tests**: Client management and validation
- **API Controller Tests**: HTTP endpoint testing with supertest
- **CLI Utility Tests**: Command-line tool testing

#### Legacy Test Commands

```bash
npm run test:username-validation      # Legacy CLI-based username tests
npm run test:api-username-validation  # Legacy API validation tests (requires server)
```
````
