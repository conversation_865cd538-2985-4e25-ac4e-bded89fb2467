# OAuth 2.0 Authentication Server with RS256 & Client Example

This project provides a **complete OAuth 2.0 ecosystem** with RS256 JWT tokens, including:

- **OAuth 2.0 Authorization Server** (Node.js/Express)
- **Example Client Application** (React/Vite)
- **Comprehensive Documentation** and examples

## 🎯 **Project Overview**

```
varpro_auth/
├── server/                 # OAuth 2.0 Authorization Server
│   ├── src/
│   │   ├── controllers/    # API endpoints
│   │   ├── services/       # Business logic
│   │   ├── db/            # Database models & migrations
│   │   ├── routes/        # Express routes
│   │   └── utils/         # Utilities & key generation
│   ├── keys/              # RSA key pairs for RS256
│   └── package.json
├── client/                # Original client (if exists)
├── client-app-example/    # Comprehensive React example
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── contexts/      # Authentication context
│   │   ├── pages/         # Route pages
│   │   ├── services/      # OAuth integration
│   │   └── config/        # Configuration

- Authorization Code Flow (with PKCE support)
- Resource Owner Password Credentials Flow
- Refresh Token Flow

## Setup

This creates:

- Test users (testuser/password123, admin/admin123)
- Sample OAuth clients for web and mobile applications

## OAuth 2.0 Endpoints

### Authorization Endpoint

```

GET /oauth/authorize?response_type=code&client_id={client_id}&redirect_uri={redirect_uri}&scope={scope}&state={state}

```

### Token Endpoint

```

POST /oauth/token
Content-Type: application/x-www-form-urlencoded

# Authorization Code Flow

grant_type=authorization_code&code={code}&redirect_uri={redirect_uri}&client_id={client_id}&client_secret={client_secret}

# Password Flow

grant_type=password&username={username}&password={password}&client_id={client_id}&client_secret={client_secret}&scope={scope}

# Refresh Token

grant_type=refresh_token&refresh_token={refresh_token}&client_id={client_id}&client_secret={client_secret}

```

### Token Revocation

```

POST /oauth/revoke
Content-Type: application/x-www-form-urlencoded

token={access_token_or_refresh_token}&client_id={client_id}&client_secret={client_secret}

```

## DB

### Migration

To create a migration
`npx kysely migrate:make table-some_table_name`
`npx kysely migrate:make column-some_column-some_table_name`

To run a single migration
`npx kysely migrate:up`

To run all unrun migrations
`npx kysely migrate:latest`

To undo the last migration run
`npx kysely migrate:down`
```
